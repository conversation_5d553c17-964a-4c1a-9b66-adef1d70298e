2025-08-07 15:16:41,250 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:16:41,263 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:16:41,267 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:16:41,297 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:16:41,306 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:16:41,311 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:16:41,320 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:16:41,323 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:16:41,341 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:16:41,351 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:16:41,355 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:17:41,434 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:17:41,443 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:17:41,451 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:17:41,463 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:17:41,465 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:17:41,469 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:17:41,480 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:17:41,483 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:17:41,485 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:17:41,488 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:17:41,508 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:17:41,515 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:17:41,525 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:17:41,534 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:17:41,536 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:17:41,543 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:17:41,547 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:18:42,108 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:18:42,112 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:18:42,120 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:18:42,127 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:18:42,136 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:18:42,154 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:18:42,157 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:18:42,160 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:18:42,168 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:18:42,179 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:18:42,188 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:18:42,194 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:18:42,195 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:18:42,197 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:18:42,199 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:18:42,204 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:18:42,217 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:19:42,252 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:19:42,268 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:19:42,274 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:19:42,279 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:19:42,282 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:19:42,283 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:19:42,293 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:19:42,297 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:19:42,301 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:19:42,305 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:19:42,309 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:19:42,316 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:19:42,320 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:19:42,327 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:19:42,331 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:19:42,333 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:19:42,352 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:20:42,672 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:20:42,689 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:20:42,695 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:20:42,720 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:20:42,760 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:20:42,788 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:21:43,125 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:21:43,131 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:21:43,145 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:21:43,160 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:21:43,180 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:21:43,189 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:21:43,190 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:21:43,210 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:22:43,568 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:22:43,613 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:23:44,143 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:23:44,148 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:23:44,161 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:23:44,164 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:23:44,181 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:23:44,189 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:23:44,193 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:23:44,203 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:24:44,263 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:24:44,266 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:24:44,269 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:24:44,280 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:24:44,291 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:24:44,294 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:24:44,297 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:24:44,327 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:25:44,609 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:25:44,611 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:25:44,629 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:25:44,646 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:25:44,651 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:25:44,654 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:25:44,668 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:25:44,672 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:26:44,935 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:26:44,942 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:26:44,966 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:26:44,975 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:26:44,986 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:26:44,989 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:26:44,993 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:26:45,007 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:27:45,350 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:27:45,353 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:27:45,355 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:27:45,369 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:27:45,374 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:27:45,375 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:27:45,392 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:27:45,433 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:28:45,923 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:28:45,931 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:28:45,938 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:28:45,940 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:28:45,954 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:28:45,983 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:28:45,988 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:28:46,000 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:29:46,319 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:29:46,328 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:29:46,341 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:29:46,347 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:29:46,359 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:29:46,370 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:29:46,384 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:29:46,398 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:30:46,696 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:30:46,705 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:30:46,719 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:30:46,727 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:30:46,749 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:30:46,754 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:30:46,789 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:30:46,794 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:31:47,035 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:31:47,039 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for rubis
2025-08-07 15:31:47,042 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:31:47,048 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:31:47,053 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:31:47,058 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:31:47,062 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:31:47,073 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:31:47,077 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-08-07 15:31:47,084 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:31:47,087 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:31:47,092 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:31:47,095 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:31:47,098 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:31:47,103 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:31:47,107 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:31:47,118 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:31:47,125 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:31:47,133 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:31:47,143 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:31:47,144 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:32:47,381 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:32:47,382 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:32:47,387 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:32:47,389 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:32:47,391 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:32:47,402 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:32:47,404 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for rubis
2025-08-07 15:32:47,424 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-08-07 15:32:47,430 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:32:47,436 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:32:47,441 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:32:47,442 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:32:47,476 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:33:47,513 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:33:47,532 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-08-07 15:33:47,534 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:33:47,545 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:33:47,549 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:33:47,552 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:33:47,560 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:33:47,563 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:33:47,568 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:33:47,572 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:33:47,577 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:33:47,585 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:33:47,591 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:33:47,594 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:33:47,604 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for rubis
2025-08-07 15:33:47,616 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:33:47,622 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:33:47,638 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:33:47,642 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:37:49,233 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:37:49,249 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:37:49,270 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:37:49,290 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:37:49,292 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:37:49,296 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:38:49,571 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:38:49,589 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:38:49,595 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:38:49,600 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:38:49,613 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:38:49,622 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:39:49,669 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:39:49,683 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:39:49,693 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:39:49,701 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:39:49,768 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:39:49,774 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:40:50,186 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:40:50,205 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:40:50,212 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:40:50,241 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:40:50,251 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:40:50,272 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:41:50,511 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:41:50,532 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:41:50,533 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:41:50,540 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:41:50,558 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:41:50,560 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:41:50,562 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:41:50,579 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:42:50,830 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:42:50,835 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:42:50,839 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:42:50,845 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:42:50,871 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:42:50,912 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:43:51,191 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:43:51,202 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:43:51,219 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:43:51,230 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:43:51,235 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:43:51,243 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:43:51,264 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:43:51,265 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:44:51,325 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:44:51,381 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:45:51,834 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:45:51,865 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:45:51,869 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:45:51,879 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:45:51,883 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:45:51,894 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:45:51,900 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:45:51,933 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:46:52,018 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:46:52,033 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:46:52,036 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:46:52,039 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:46:52,049 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:46:52,053 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:46:52,055 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:46:52,066 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:46:52,070 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:46:52,076 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:46:52,083 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:46:52,100 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:46:52,103 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:46:52,104 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:46:52,107 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:46:52,111 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:46:52,117 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:46:52,125 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:46:52,129 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:47:52,384 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:47:52,395 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:47:52,415 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:47:52,417 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:47:52,419 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:47:52,421 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:47:52,433 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:47:52,435 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:47:52,436 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:47:52,447 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:47:52,463 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:47:52,472 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:47:52,478 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:47:52,479 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:47:52,485 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:47:52,492 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:47:52,499 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:47:52,502 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:47:52,506 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:48:53,082 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:48:53,091 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:48:53,094 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:48:53,107 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:48:53,119 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:48:53,130 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:48:53,134 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:48:53,137 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:48:53,148 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:48:53,149 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:48:53,151 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:48:53,154 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:48:53,159 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:48:53,161 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:48:53,165 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:48:53,171 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:48:53,175 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:48:53,178 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:48:53,181 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:49:53,245 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:49:53,249 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:49:53,252 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:49:53,266 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:49:53,268 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:49:53,271 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:49:53,278 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:49:53,286 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:49:53,289 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:49:53,294 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:49:53,299 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:49:53,300 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:49:53,309 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:49:53,321 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:49:53,342 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:49:53,350 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:49:53,357 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:49:53,360 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:49:53,366 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:50:53,764 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:50:53,777 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:50:53,779 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:50:53,798 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:50:53,801 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:50:53,809 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:50:53,812 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:50:53,822 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:50:53,823 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:50:53,835 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:50:53,837 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:50:53,852 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:50:53,864 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:51:53,918 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:51:53,921 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:51:53,924 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:51:53,933 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:51:53,951 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:51:53,957 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:51:53,961 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:51:53,993 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:51:54,017 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:51:54,029 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:51:54,042 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:51:54,044 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:51:54,048 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:52:54,296 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:52:54,306 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:52:54,313 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:52:54,317 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:52:54,322 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:52:54,329 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:52:54,339 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:52:54,354 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:52:54,356 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:52:54,363 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:52:54,387 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:52:54,405 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:52:54,407 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:53:54,909 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:53:54,911 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:53:54,915 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:53:54,925 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:53:54,932 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:53:54,938 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:53:54,945 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:53:54,956 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:53:54,958 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:53:54,962 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:53:54,967 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:53:54,973 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:53:54,975 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:53:54,983 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:53:54,993 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:53:54,998 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:53:55,005 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:53:55,007 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:53:55,011 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:54:55,113 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:54:55,118 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:54:55,122 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:54:55,129 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:54:55,131 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 15:54:55,147 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:54:55,151 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:54:55,157 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:54:55,159 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:54:55,168 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:54:55,169 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:54:55,175 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:54:55,182 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:54:55,192 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:54:55,203 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:54:55,207 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:54:55,215 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:54:55,218 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:54:55,224 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 15:55:55,512 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:55:55,523 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:55:55,526 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:55:55,533 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:55:55,560 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:55:55,565 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:55:55,569 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:55:55,582 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:55:55,586 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:55:55,592 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:55:55,595 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:56:55,828 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:56:55,858 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:56:55,894 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 16:01:58,168 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-07 16:01:58,205 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-07 16:01:58,217 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-07 16:01:58,232 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-07 16:01:58,319 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-07 16:01:58,345 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-07 16:01:58,359 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-07 16:01:58,371 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-07 16:01:58,445 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-07 16:01:58,453 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-07 16:01:58,475 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-07 16:01:58,521 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-07 16:01:58,537 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-07 16:02:58,583 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-07 16:02:58,611 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-07 16:02:58,615 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-07 16:02:58,617 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-07 16:02:58,637 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-07 16:02:58,655 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-07 16:02:58,664 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-07 16:02:58,672 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-07 16:02:58,678 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-07 16:02:58,680 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-07 16:02:58,684 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-07 16:02:58,690 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-07 16:02:58,712 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-07 16:03:58,895 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-07 16:03:58,911 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-07 16:03:58,919 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-07 16:03:58,943 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-07 16:03:58,953 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-07 16:03:58,958 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-07 16:03:58,961 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-07 16:03:58,965 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-07 16:03:58,970 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-07 16:03:58,984 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-07 16:03:59,010 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-07 16:03:59,017 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-07 16:03:59,039 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-07 16:04:59,559 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-07 16:04:59,561 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-07 16:04:59,569 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-07 16:04:59,607 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-07 16:04:59,616 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-07 16:04:59,639 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-07 16:04:59,642 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-07 16:04:59,656 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-07 16:04:59,660 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-07 16:04:59,669 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-07 16:04:59,675 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-07 16:04:59,678 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-07 16:04:59,689 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-07 16:05:59,720 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-07 16:05:59,723 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-07 16:05:59,736 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-07 16:05:59,744 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 16:05:59,755 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-07 16:05:59,759 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-07 16:05:59,766 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 16:05:59,771 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 16:05:59,783 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-07 16:05:59,785 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-07 16:05:59,799 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 16:05:59,813 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-07 16:05:59,822 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-07 16:05:59,847 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 16:05:59,851 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-07 16:05:59,855 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-07 16:05:59,860 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-07 16:05:59,868 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 16:05:59,876 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-07 16:07:00,322 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-07 16:07:00,325 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-07 16:07:00,331 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-07 16:07:00,335 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-07 16:07:00,343 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-07 16:07:00,345 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 16:07:00,384 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-07 16:07:00,391 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 16:07:00,395 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-07 16:07:00,407 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-07 16:07:00,414 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-07 16:07:00,420 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-07 16:07:00,426 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 16:07:00,442 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-07 16:07:00,445 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-07 16:07:00,456 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-07 16:07:00,462 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 16:07:00,467 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 16:07:00,470 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 16:08:00,744 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-07 16:08:00,750 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-07 16:08:00,757 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-07 16:08:00,761 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-07 16:08:00,767 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 16:08:00,776 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 16:08:00,779 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 16:08:00,782 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-07 16:08:00,792 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-07 16:08:00,797 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-07 16:08:00,819 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-07 16:08:00,826 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 16:08:00,842 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-07 16:08:00,854 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 16:08:00,866 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-07 16:08:00,872 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 16:08:00,877 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-07 16:08:00,882 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-07 16:08:00,948 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-07 16:09:01,584 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-07 16:09:01,624 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-07 16:09:01,629 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-07 16:09:01,634 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-07 16:09:01,638 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-07 16:09:01,642 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-07 16:09:01,655 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-07 16:09:01,659 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-07 16:09:01,664 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-07 16:09:01,699 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-07 16:09:01,713 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-07 16:09:01,716 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-07 16:09:01,766 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-07 16:10:02,169 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-07 16:10:02,184 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-07 16:10:02,194 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-07 16:10:02,202 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-07 16:10:02,225 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-07 16:10:02,238 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-07 16:10:02,262 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-07 16:10:02,265 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-07 16:10:02,267 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-07 16:10:02,273 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-07 16:10:02,276 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-07 16:10:02,278 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-07 16:10:02,282 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-07 16:11:02,656 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-07 16:11:02,679 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 16:11:02,694 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-07 16:11:02,696 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-07 16:11:02,715 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 16:11:02,717 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-07 16:11:02,728 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-07 16:11:02,733 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-07 16:11:02,740 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-07 16:11:02,743 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-07 16:11:02,750 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-07 16:11:02,773 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-07 16:11:02,787 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-07 16:11:02,801 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-07 16:11:02,817 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-07 16:12:02,927 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-07 16:12:02,937 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-07 16:12:02,943 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-07 16:12:02,945 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-07 16:12:02,956 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-07 16:12:02,970 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-07 16:12:02,973 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-07 16:12:02,987 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-07 16:12:02,991 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-07 16:12:02,994 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-07 16:12:03,000 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-07 16:12:03,013 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-07 16:12:03,028 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-07 16:13:03,274 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-07 16:13:03,276 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 16:13:03,278 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 16:13:03,281 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-07 16:13:03,283 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-07 16:13:03,286 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-07 16:13:03,288 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 16:13:03,294 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-07 16:13:03,302 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-07 16:13:03,313 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-07 16:13:03,315 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-07 16:13:03,333 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 16:13:03,348 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-07 16:13:03,355 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-07 16:13:03,362 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-07 16:13:03,390 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 16:13:03,399 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 16:13:03,407 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-07 16:13:03,412 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-07 16:13:03,415 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 16:13:03,418 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 16:14:03,664 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-07 16:14:03,680 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-07 16:14:03,689 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-07 16:14:03,698 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-07 16:14:03,705 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-07 16:14:03,709 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-07 16:14:03,716 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 16:14:03,721 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-07 16:14:03,730 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 16:14:03,737 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 16:14:03,738 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-07 16:14:03,741 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-07 16:14:03,743 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-07 16:14:03,745 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-07 16:14:03,748 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-07 16:14:03,753 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 16:14:03,763 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-07 16:14:03,770 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 16:14:03,779 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 16:14:03,781 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-07 16:14:03,784 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-07 16:15:04,015 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-07 16:15:04,017 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 16:15:04,029 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-07 16:15:04,039 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-07 16:15:04,052 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-07 16:15:04,084 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-07 16:15:04,086 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 16:15:04,090 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-07 16:15:04,095 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-07 16:15:04,100 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-07 16:15:04,111 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 16:15:04,125 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 16:15:04,127 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 16:15:04,150 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-07 16:15:04,153 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
